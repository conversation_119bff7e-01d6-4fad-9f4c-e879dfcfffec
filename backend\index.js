const express = require('express');
const cors = require('cors');
const bodyParser = require('body-parser');

const app = express();
const PORT = 5000;

app.use(cors());
app.use(bodyParser.json());

// ذكاء اصطناعي بسيط (محلي)
function simpleAI(question) {
    // يمكنك تطوير هذه الدالة لاحقًا أو ربطها بذكاء اصطناعي مفتوح المصدر
    if (question.toLowerCase().includes('مرحبا')) return 'أهلاً بك! كيف يمكنني مساعدتك اليوم؟';
    if (question.toLowerCase().includes('اسمك')) return 'أنا مساعدك الذكي المجاني!';
    return 'عذراً، لم أفهم سؤالك. حاول مرة أخرى.';
}

app.post('/api/ask', (req, res) => {
    const { question } = req.body;
    const answer = simpleAI(question || '');
    res.json({ answer });
});

app.listen(PORT, () => {
    console.log(`AI Assistant backend running on http://localhost:${PORT}`);
});
P